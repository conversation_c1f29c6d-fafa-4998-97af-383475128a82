const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

const attendanceService = require('../../../../services/attendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API thống kê điểm danh chi tiết theo khoảng thời gian (mặc định là tuần)
 * POST /api/v1.0/statisticsDetail/attendance/total
 *
 * Input:
 * - period: enum ['week', 'month', 'quarter'] - khoảng thời gian thống kê (mặc định là 'week')
 * - startDate: string (DD-MM-YYYY) - ngày bắt đầu
 * - endDate: string (DD-MM-YYYY) - ng<PERSON><PERSON> kết thúc
 *
 * Output:
 * - summary: tổng quan thống kê
 * - byDay: thống kê theo từng ngày
 * - byUser: thống kê theo từng user
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    period = 'week',
    startDate,
    endDate
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Khoảng thời gian thống kê
      period: Joi.string().valid('week', 'month', 'quarter').default('week').optional(),

      // Ngày bắt đầu và kết thúc - định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate
    if (startDate && endDate && DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    // Kiểm tra khoảng thời gian hợp lý
    const maxDays = getMaxDaysForPeriod(period);
    const daysDiff = startDate && endDate && calculateDaysDifference(startDate, endDate);

    if (daysDiff && daysDiff > maxDays) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Khoảng thời gian không hợp lệ',
          body: `Khoảng thời gian ${period} không được vượt quá ${maxDays} ngày`
        }
      });
    }

    next();
  };

  const getStatistics = (next) => {
    try {
      attendanceService.getDetailedAttendanceStatistics(
        period,
        startDate,
        endDate,
        userId
      )
        .then((result) => {
          if (!result || !result.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: result.message || {
                head: 'Lỗi hệ thống',
                body: 'Không thể lấy thống kê điểm danh'
              }
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: result.message,
            data: result.data
          });
        })
        .catch((err) => {
          console.error('Error in getDetailedAttendanceStatistics:', err);
          next(err);
        });
    } catch (error) {
      console.error('Error in getStatistics:', error);
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getStatistics
  ], (err, data) => {
    if (_.isError(err)) {
      console.error('Error in attendance statistics API:', err);
    }

    // Nếu có lỗi và là Error object, trả về lỗi hệ thống
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Đã xảy ra lỗi không mong muốn'
        }
      };
    }

    res.json(data || err);
  });
};

/**
 * Lấy số ngày tối đa cho từng loại period
 * @param {String} period - Loại khoảng thời gian
 * @returns {Number} Số ngày tối đa
 */
function getMaxDaysForPeriod(period) {
  switch (period) {
    case 'week':
      return 7;
    case 'month':
      return 31;
    case 'quarter':
      return 93; // 3 tháng
    default:
      return 31;
  }
}

/**
 * Tính số ngày giữa hai ngày
 * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
 * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
 * @returns {Number} Số ngày chênh lệch
 */
function calculateDaysDifference(startDate, endDate) {
  const [startDay, startMonth, startYear] = startDate.split('-').map(Number);
  const [endDay, endMonth, endYear] = endDate.split('-').map(Number);

  const start = new Date(startYear, startMonth - 1, startDay);
  const end = new Date(endYear, endMonth - 1, endDay);

  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
}
