/**
 * Service xử lý logic điểm danh
 * Quản lý điểm danh, lịch sử và thống kê chuyên cần
 */

const _ = require('lodash');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const User = require('../models/user');
const LeaveRequest = require('../models/leaveRequest');
const MeetingSchedule = require('../models/meetingSchedule');
const attendancePermission = require('../util/attendancePermission');
const NotificationHelper = require('../util/notificationHelper');
const attendanceCache = require('../util/attendanceCache');
const DateUtils = require('../utils/dateUtils');
const CONSTANTS = require('../const');

class AttendanceService {
  /**
   * Điểm danh cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Object} location - <PERSON>ị trí điểm danh (optional)
   * @param {String} note - <PERSON><PERSON> tả điểm danh (optional)
   * @returns {Object} Kết quả điểm danh
   */
  async checkin(userId, location = null, note = '') {
    try {
      const currentDate = DateUtils.getCurrentDateDDMMYYYY();

      // Tìm lịch làm việc
      const schedule = await WorkSchedule.findOne({
        date: currentDate,
        user: userId,
        status: 1
      });
      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không có lịch làm việc',
            body: 'Không có lịch làm việc cho ngày hôm nay'
          },
          data: null
        };
      }

      // Tìm ca làm việc hiện tại
      const currentTime = new Date();
      const currentShift = this.getCurrentShift(schedule.shifts, currentTime);
      if (!currentShift) {
        return {
          success: false,
          message: {
            head: 'Không có ca làm việc',
            body: 'Không có ca làm việc nào trong thời gian hiện tại'
          },
          data: null
        };
      }

      // Kiểm tra đã điểm danh chưa
      const existingRecord = await AttendanceRecord.findOne({
        user: userId,
        date: currentDate,
        shift: currentShift.type
      });

      if (existingRecord) {
        return {
          success: false,
          message: {
            head: 'Đã điểm danh',
            body: 'Bạn đã điểm danh cho ca làm việc này'
          },
          data: existingRecord
        };
      }

      // Xác định trạng thái điểm danh
      const status = this.determineAttendanceStatus(currentShift, currentTime);

      // Tạo bản ghi điểm danh
      const attendanceData = {
        user: userId,
        schedule: schedule._id,
        date: currentDate,
        shift: currentShift.type,
        checkinTime: currentTime.getTime(),
        status: status,
        location: location,
        note: note
      };

      const attendanceRecord = await AttendanceRecord.create(attendanceData);

      // Cập nhật trạng thái ca làm việc trong schedule
      const shiftIndex = schedule.shifts.findIndex(s => s.type === currentShift.type);
      if (shiftIndex !== -1) {
        schedule.shifts[shiftIndex].status = status;
        schedule.shifts[shiftIndex].checkinTime = currentTime.getTime();
        if (location) {
          schedule.shifts[shiftIndex].location = location;
        }
        schedule.updatedAt = Date.now();
        await schedule.save();
      }

      // Gửi thông báo điểm danh thành công
      NotificationHelper.notifySuccessfulCheckin(userId, {
        status,
        shift: currentShift.type,
        checkinTime: currentTime.getTime()
      });

      // Invalidate cache
      attendanceCache.invalidateTodayStatus(userId, currentDate);
      attendanceCache.invalidateAttendanceStats(userId);

      return {
        success: true,
        message: status === 'on_time' ? {
          head: 'Điểm danh thành công',
          body: 'Bạn đã điểm danh thành công'
        } : {
          head: 'Điểm danh muộn',
          body: 'Bạn đã điểm danh muộn'
        },
        data: {
          attendanceRecord,
          status,
          checkinTime: currentTime.getTime()
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy lịch sử điểm danh
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Lịch sử điểm danh
   */
  async getAttendanceHistory(userId, startDate, endDate) {
    try {
      const query = {
        user: userId
      };

      if (startDate && endDate) {
        query.date = {
          $gte: startDate,
          $lte: endDate
        };
      } else if (startDate) {
        query.date = { $gte: startDate };
      } else if (endDate) {
        query.date = { $lte: endDate };
      }

      const records = await AttendanceRecord.find(query)
        .populate('user', 'name idNumber')
        .populate('schedule')
        .sort({ date: -1, checkinTime: -1 })
        .lean();

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch sử điểm danh thành công'
        },
        data: records
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Kiểm tra trạng thái điểm danh hôm nay
   * @param {String} userId - ID cán bộ
   * @param {String} date - Ngày kiểm tra (DD-MM-YYYY)
   * @param {String} shift - Ca làm việc (optional)
   * @returns {Object} Trạng thái điểm danh
   */
  async checkAttendanceStatus(userId, date, shift = null) {
    try {
      const query = {
        user: userId,
        date: date
      };

      if (shift) {
        query.shift = shift;
      }

      const records = await AttendanceRecord.find(query)
        .populate('schedule')
        .lean();

      // Lấy lịch làm việc của ngày đó
      const schedule = await WorkSchedule.findOne({
        user: userId,
        date: date,
        status: 1
      }).lean();

      const result = {
        date,
        hasSchedule: !!schedule,
        shifts: [],
        summary: {
          total: 0,
          completed: 0,
          absent: 0,
          nonattendance: 0
        }
      };

      if (schedule) {
        result.shifts = schedule.shifts.map(scheduleShift => {
          const attendanceRecord = records.find(r => r.shift === scheduleShift.type);
          const shiftStatus = this.getShiftStatus(scheduleShift, attendanceRecord, date);

          result.summary.total++;
          result.summary[shiftStatus]++;

          return {
            type: scheduleShift.type,
            startTime: scheduleShift.startTime,
            status: shiftStatus,
            checkinTime: attendanceRecord?.checkinTime,
            attendanceStatus: attendanceRecord?.status
          };
        });
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Kiểm tra trạng thái điểm danh thành công'
        },
        data: result
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê chuyên cần
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Thống kê chuyên cần
   */
  async getAttendanceStatistics(userId, startDate, endDate) {
    try {
      // Lấy dữ liệu điểm danh
      const query = {
        user: userId,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };

      const [attendanceRecords, workSchedules] = await Promise.all([
        AttendanceRecord.find(query).populate('user', 'name idNumber units avatar').lean(),
        WorkSchedule.find({
          user: userId,
          date: {
            $gte: startDate,
            $lte: endDate
          },
          status: 1
        }).populate('user', 'name idNumber units avatar').lean()
      ]);

      // Tính toán thống kê
      const statistics = await this.calculateStatistics(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê chuyên cần thành công'
        },
        data: {
          period: { startDate, endDate },
          statistics: statistics[0]
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Thống kê chuyên cần (Admin)
   * @param {String} userId - ID cán bộ (optional, nếu không có thì thống kê theo quyền)
   * @param {String} unitId - ID đơn vị (optional)
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {String} viewerId - ID người xem thống kê
   * @returns {Object} Thống kê chuyên cần
   */
  async getAttendanceStatisticsAdmin(viewerId, userId = null, unitId = null, startDate, endDate) {
    try {
      // Kiểm tra quyền xem thống kê
      // const permissionCheck = await attendancePermission.checkStatisticsPermission(viewerId, userId);

      // if (!permissionCheck.canView) {
      //   return {
      //     success: false,
      //     message: {
      //       head: 'Không có quyền',
      //       body: permissionCheck.message
      //     },
      //     data: null
      //   };
      // }

      // let targetUserIds = [];

      // if (userId) {
      //   // Thống kê cho một cán bộ cụ thể
      //   targetUserIds = [userId];
      // } else {
      //   // Thống kê cho nhiều cán bộ theo quyền
      //   if (permissionCheck.scope === 'all') {
      //     // Lấy tất cả cán bộ
      //     const allUsers = await User.find({ status: 1 }).select('_id').lean();
      //     targetUserIds = allUsers.map(u => u._id.toString());
      //   } else {
      //     // Lấy cán bộ cùng đơn vị
      //     targetUserIds = await attendancePermission.getManagedUsers(viewerId);
      //   }

      //   // Lọc theo đơn vị nếu có
      //   if (unitId) {
      //     const unitUsers = await User.find({
      //       units: unitId,
      //       status: 1
      //     }).select('_id').lean();
      //     const unitUserIds = unitUsers.map(u => u._id.toString());
      //     targetUserIds = targetUserIds.filter(id => unitUserIds.includes(id));
      //   }
      // }

      // Lấy dữ liệu điểm danh
      const query = {
        // user: { $in: targetUserIds },
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };
      // if (targetUserIds.length) {
      //   query.user = { $in: targetUserIds };
      // }

      const [attendanceRecords, workSchedules] = await Promise.all([
        AttendanceRecord.find(query).populate('user', 'name idNumber units avatar').lean(),
        WorkSchedule.find({
          // user: { $in: targetUserIds },
          date: {
            $gte: startDate,
            $lte: endDate
          },
          status: 1
        })
        .populate({
          path: 'user',
          select: 'name idNumber units avatar',
          populate: {
            path: 'units',
            select: 'name'
          }
        })
        .lean()
      ]);

      // Tính toán thống kê
      const statistics = await this.calculateStatistics(attendanceRecords, workSchedules);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê chuyên cần thành công'
        },
        data: {
          period: { startDate, endDate },
          // scope: permissionCheck.scope,
          statistics
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tìm ca làm việc hiện tại
   * @param {Array} shifts - Danh sách ca làm việc
   * @param {Date} currentTime - Thời gian hiện tại
   * @returns {Object|null} Ca làm việc hiện tại
   */
  getCurrentShift(shifts, currentTime) {
    const currentHour = currentTime.getHours();
    const currentMinute = currentTime.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Ca sáng: 8:00 (có thể điểm danh từ 7:30 đến 12:00)
    // Ca chiều: 14:00 (có thể điểm danh từ 13:30 đến 18:00)

    for (const shift of shifts) {
      if (shift.type === 'morning') {
        // Ca sáng: 7:30 - 12:00
        if (currentTimeInMinutes >= 7 * 60 + 30 && currentTimeInMinutes <= 12 * 60) { // 7:30 - 12:00
          return shift;
        }
      } else if (shift.type === 'afternoon') {
        // Ca chiều: 13:30 - 18:00
        if (currentTimeInMinutes >= 13 * 60 + 30 && currentTimeInMinutes <= 18 * 60) { // 13:30 - 18:00
          return shift;
        }
      }
    }

    return null;
  }

  /**
   * Xác định trạng thái điểm danh
   * @param {Object} shift - Ca làm việc
   * @param {Date} checkinTime - Thời gian điểm danh
   * @returns {String} Trạng thái điểm danh
   */
  determineAttendanceStatus(shift, checkinTime) {
    const checkinHour = checkinTime.getHours();
    const checkinMinute = checkinTime.getMinutes();
    const checkinTimeInMinutes = checkinHour * 60 + checkinMinute;

    // Sử dụng constants cho thời gian bắt đầu làm việc
    const workStartTime = shift.type === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_WORK_START
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_WORK_START;

    return checkinTimeInMinutes <= workStartTime ? 'on_time' : 'late';
  }

  /**
   * Lấy trạng thái ca làm việc (đã cải tiến để phân biệt absent và nonattendance)
   * @param {Object} scheduleShift - Ca trong lịch làm việc
   * @param {Object} attendanceRecord - Bản ghi điểm danh
   * @param {String} scheduleDate - Ngày của ca làm việc (DD-MM-YYYY)
   * @returns {String} Trạng thái ca
   */
  getShiftStatus(scheduleShift, attendanceRecord, scheduleDate = null) {
    if (attendanceRecord) {
      return 'completed';
    }

    // Nếu không có ngày, sử dụng logic cũ
    if (!scheduleDate) {
      const now = new Date();
      const currentHour = now.getHours();
      const workStartHour = scheduleShift.type === 'morning' ? 8 : 14;

      if (currentHour > workStartHour + 4) {
        return 'absent';
      }
      return 'nonattendance';
    }

    // Kiểm tra trạng thái 'excused' và 'business_trip' - giữ nguyên
    if (scheduleShift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.EXCUSED ||
        scheduleShift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.BUSINESS_TRIP) {
      return scheduleShift.status;
    }

    // Sử dụng logic mới với ngày cụ thể
    if (this.isShiftCompleted(scheduleDate, scheduleShift.type)) {
      return 'absent'; // Ca đã kết thúc mà không có chấm công
    }

    // Kiểm tra nếu đang trong thời gian checkin và trạng thái là 'scheduled'
    if (scheduleShift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.SCHEDULED) {
      const now = new Date();
      const currentDate = DateUtils.getCurrentDateDDMMYYYY();

      // Chỉ áp dụng cho ngày hôm nay
      if (DateUtils.compareDDMMYYYY(scheduleDate, currentDate) === 0) {
        const currentTimeInMinutes = now.getHours() * 60 + now.getMinutes();

        const checkinStartTime = scheduleShift.type === 'morning'
          ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START
          : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START;

        const checkinEndTime = scheduleShift.type === 'morning'
          ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_END
          : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_END;

        // Nếu trong thời gian checkin và chưa có bản ghi điểm danh
        if (currentTimeInMinutes >= checkinStartTime && currentTimeInMinutes <= checkinEndTime) {
          return 'nonattendance';
        }
      }
    }

    return scheduleShift.status; // Trả về trạng thái gốc
  }

  /**
   * Thống kê điểm danh chi tiết theo khoảng thời gian
   * @param {String} period - Khoảng thời gian ('week', 'month', 'quarter')
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @param {String} viewerId - ID người xem thống kê
   * @returns {Object} Thống kê chi tiết điểm danh
   */
  async getDetailedAttendanceStatistics(period, startDate, endDate, viewerId) {
    try {
      // Lấy tất cả dữ liệu cần thiết
      const [attendanceRecords, workSchedules, leaveRequests, users] = await Promise.all([
        // Lấy tất cả bản ghi điểm danh trong khoảng thời gian
        AttendanceRecord.find({
          date: { $gte: startDate, $lte: endDate }
        }).populate('user', 'name idNumber').lean(),

        // Lấy tất cả lịch làm việc trong khoảng thời gian
        WorkSchedule.find({
          date: { $gte: startDate, $lte: endDate },
          status: 1
        }).populate('user', 'name idNumber').lean(),

        // Lấy tất cả đơn nghỉ phép đã được duyệt
        LeaveRequest.find({
          startDate: { $lte: endDate },
          $or: [
            { endDate: { $gte: startDate } },
            { endDate: { $exists: false } }
          ],
          status: 'approved'
        }).populate('user', 'name idNumber').lean(),

        // Lấy danh sách tất cả user để tính tổng số nhân viên
        User.find({ status: 1 }, 'name idNumber').lean()
      ]);

      // Tính toán thống kê
      const statistics = this.calculateDetailedStatistics(
        attendanceRecords,
        workSchedules,
        leaveRequests,
        users,
        startDate,
        endDate
      );

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê điểm danh chi tiết thành công'
        },
        data: statistics
      };

    } catch (error) {
      console.error('Error in getDetailedAttendanceStatistics:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán thống kê chi tiết điểm danh
   * @param {Array} attendanceRecords - Danh sách bản ghi điểm danh
   * @param {Array} workSchedules - Danh sách lịch làm việc
   * @param {Array} leaveRequests - Danh sách đơn nghỉ phép
   * @param {Array} users - Danh sách tất cả user
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @returns {Object} Kết quả thống kê chi tiết
   */
  calculateDetailedStatistics(attendanceRecords, workSchedules, leaveRequests, users, startDate, endDate) {
    // Tạo map để tra cứu nhanh
    const attendanceMap = new Map();
    const scheduleMap = new Map();
    const leaveMap = new Map();
    const userMap = new Map();

    // Index attendance records theo user-date-shift
    attendanceRecords.forEach(record => {
      const key = `${record.user._id}-${record.date}-${record.shift}`;
      attendanceMap.set(key, record);
    });

    // Index work schedules theo user-date
    workSchedules.forEach(schedule => {
      const key = `${schedule.user._id}-${schedule.date}`;
      if (!scheduleMap.has(key)) {
        scheduleMap.set(key, []);
      }
      scheduleMap.get(key).push(schedule);
    });

    // Index leave requests theo user
    leaveRequests.forEach(leave => {
      const userId = leave.user._id.toString();
      if (!leaveMap.has(userId)) {
        leaveMap.set(userId, []);
      }
      leaveMap.get(userId).push(leave);
    });

    // Index users
    users.forEach(user => {
      userMap.set(user._id.toString(), user);
    });

    // Tạo danh sách ngày trong khoảng thời gian
    const dateList = this.generateDateRange(startDate, endDate);

    // Khởi tạo counters
    const summary = {
      totalEmployees: users.length,
      onTimeAttendance: 0,
      lateAttendance: 0,
      nonAttendance: 0,
      absent: 0,
      excused: 0,
      businessTrip: 0,
      totalShifts: 0
    };

    const byDay = [];
    const byUserMap = new Map();

    // Khởi tạo byUser map
    users.forEach(user => {
      byUserMap.set(user._id.toString(), {
        userId: user._id.toString(),
        userName: user.name,
        totalAbsent: 0,
        totalLate: 0
      });
    });

    // Duyệt qua từng ngày
    dateList.forEach(date => {
      const dayStats = {
        date: date,
        onTime: 0,
        late: 0,
        nonattendance: 0,
        absent: 0,
        excused: 0,
        businessTrip: 0
      };

      // Duyệt qua từng user
      users.forEach(user => {
        const userId = user._id.toString();
        const scheduleKey = `${userId}-${date}`;
        const userSchedules = scheduleMap.get(scheduleKey) || [];
        const userStats = byUserMap.get(userId);

        // Kiểm tra đơn nghỉ phép cho ngày này
        const userLeaves = leaveMap.get(userId) || [];
        const hasApprovedLeave = userLeaves.some(leave =>
          this.isDateInLeaveRange(date, leave.startDate, leave.endDate || leave.startDate)
        );

        // Duyệt qua từng ca làm việc
        userSchedules.forEach(schedule => {
          schedule.shifts.forEach(shift => {
            summary.totalShifts++;

            const attendanceKey = `${userId}-${date}-${shift.type}`;
            const attendanceRecord = attendanceMap.get(attendanceKey);

            if (hasApprovedLeave) {
              // Có đơn nghỉ phép được duyệt
              summary.excused++;
              dayStats.excused++;
            } else if (shift.status === 'business_trip') {
              // Công tác
              summary.businessTrip++;
              dayStats.businessTrip++;
            } else if (shift.status === 'excused') {
              // Nghỉ phép (từ schedule)
              summary.excused++;
              dayStats.excused++;
            } else if (shift.status === 'absent') {
              // Vắng mặt
              summary.absent++;
              dayStats.absent++;
              userStats.totalAbsent++;
            } else if (attendanceRecord) {
              // Có điểm danh
              if (attendanceRecord.status === 'on_time') {
                summary.onTimeAttendance++;
                dayStats.onTime++;
              } else if (attendanceRecord.status === 'late') {
                summary.lateAttendance++;
                dayStats.late++;
                userStats.totalLate++;
              }
            } else if (shift.status === 'scheduled') {
              // Có lịch nhưng chưa điểm danh
              summary.nonAttendance++;
              dayStats.nonattendance++;
            }
          });
        });
      });

      byDay.push(dayStats);
    });

    // Chuyển byUserMap thành array và lọc những user có dữ liệu
    const byUser = Array.from(byUserMap.values())
      .filter(user => user.totalAbsent > 0 || user.totalLate > 0)
      .sort((a, b) => (b.totalAbsent + b.totalLate) - (a.totalAbsent + a.totalLate));

    return {
      summary,
      byDay,
      byUser
    };
  }

  /**
   * Tạo danh sách ngày trong khoảng thời gian
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách ngày
   */
  generateDateRange(startDate, endDate) {
    const dates = [];
    const start = this.parseDDMMYYYY(startDate);
    const end = this.parseDDMMYYYY(endDate);

    const current = new Date(start);
    while (current <= end) {
      dates.push(this.formatDDMMYYYY(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }

  /**
   * Parse ngày từ format DD-MM-YYYY thành Date object
   * @param {String} dateStr - Ngày dạng DD-MM-YYYY
   * @returns {Date} Date object
   */
  parseDDMMYYYY(dateStr) {
    const [day, month, year] = dateStr.split('-');
    return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  }

  /**
   * Format Date object thành DD-MM-YYYY
   * @param {Date} date - Date object
   * @returns {String} Ngày dạng DD-MM-YYYY
   */
  formatDDMMYYYY(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  /**
   * Kiểm tra ngày có nằm trong khoảng nghỉ phép không
   * @param {String} date - Ngày cần kiểm tra (DD-MM-YYYY)
   * @param {String} startDate - Ngày bắt đầu nghỉ (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc nghỉ (DD-MM-YYYY)
   * @returns {Boolean} True nếu nằm trong khoảng
   */
  isDateInLeaveRange(date, startDate, endDate) {
    const checkDate = this.parseDDMMYYYY(date);
    const leaveStart = this.parseDDMMYYYY(startDate);
    const leaveEnd = this.parseDDMMYYYY(endDate);

    return checkDate >= leaveStart && checkDate <= leaveEnd;
  }

  /**
   * Thống kê chuyên cần theo ngày với hỗ trợ filter theo ca (Admin)
   * @param {String} viewerId - ID người xem thống kê
   * @param {String} unitId - ID đơn vị (optional)
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {String} shift - Ca làm việc filter (optional: 'morning', 'afternoon')
   * @returns {Object} Thống kê chuyên cần theo ngày với hỗ trợ shift filter
   */
  async getAttendanceStatisticsByDay(viewerId, unitId = null, startDate, endDate, shift = null) {
    try {
      // Lấy dữ liệu điểm danh và lịch làm việc
      const attendanceQuery = {
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };

      // Nếu có shift filter, thêm vào query cho attendance
      if (shift) {
        attendanceQuery.shift = shift;
      }

      const [attendanceRecords, workSchedules] = await Promise.all([
        AttendanceRecord.find(attendanceQuery).populate('user', 'name idNumber units avatar').lean(),
        WorkSchedule.find({
          date: {
            $gte: startDate,
            $lte: endDate
          },
          status: 1
        }).populate('user', 'name idNumber units avatar').lean()
      ]);

      // Tính toán thống kê theo ngày với shift filter
      const dailyStatistics = await this.calculateDailyStatistics(
        attendanceRecords,
        workSchedules,
        startDate,
        endDate,
        unitId,
        shift
      );

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: shift
            ? `Lấy thống kê điểm danh theo ngày cho ca ${shift === 'morning' ? 'sáng' : 'chiều'} thành công`
            : 'Lấy thống kê điểm danh theo ngày thành công'
        },
        data: {
          period: { startDate, endDate },
          shift: shift || null,
          dailyStatistics
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán thống kê điểm danh theo ngày với hỗ trợ shift filter
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} workSchedules - Lịch làm việc
   * @param {String} startDate - Ngày bắt đầu
   * @param {String} endDate - Ngày kết thúc
   * @param {String} unitId - ID đơn vị filter (optional)
   * @param {String} shift - Ca làm việc filter (optional)
   * @returns {Array} Thống kê theo từng ngày với hỗ trợ shift filter
   */
  async calculateDailyStatistics(attendanceRecords, workSchedules, startDate, endDate, unitId = null, shift = null) {
    // Tạo danh sách tất cả ngày trong khoảng thời gian
    const allDates = DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);

    // Group dữ liệu theo ngày
    const attendanceByDate = _.groupBy(attendanceRecords, 'date');
    const schedulesByDate = _.groupBy(workSchedules, 'date');

    const dailyStats = [];

    for (const date of allDates) {
      const dayAttendance = attendanceByDate[date] || [];
      let daySchedules = schedulesByDate[date] || [];

      // Nếu có shift filter, lọc workSchedules theo shift
      if (shift) {
        daySchedules = daySchedules.filter(schedule =>
          schedule.shifts.some(s => s.type === shift)
        );
        // Cũng cần lọc shifts trong mỗi schedule
        daySchedules = daySchedules.map(schedule => ({
          ...schedule,
          shifts: schedule.shifts.filter(s => s.type === shift)
        }));
      }

      let dayStats;

      if (shift) {
        // Khi có shift filter: trả về thống kê chi tiết của ca được chọn
        const summary = await this.calculateShiftSummary(dayAttendance, daySchedules, shift);
        if (unitId) {
          const byUnit = await this.calculateDayStatsByUnitWithShift(dayAttendance, daySchedules, unitId, shift);
          dayStats = {
            date,
            shift,
            summary,
            byUnit
          };
        } else {
          dayStats = {
            date,
            shift,
            summary
          };
        }
      } else {
        // Khi không có shift filter: trả về thống kê tổng hợp + breakdown theo ca
        const summary = await this.calculateSimpleDaySummary(dayAttendance, daySchedules);
        const shift = await this.calculateShiftBreakdown(dayAttendance, daySchedules);
        if (unitId) {
          const byUnit = await this.calculateDayStatsByUnit(dayAttendance, daySchedules, unitId);
          dayStats = {
            date,
            summary,
            byUnit,
            shift
          };
        } else {
          dayStats = {
            date,
            summary,
            shift
          };
        }
      }

      dailyStats.push(dayStats);
    }

    return dailyStats;
  }

  /**
   * Tính toán thống kê đầy đủ cho một ngày (tương tự calculateStatistics)
   * @param {Array} dayAttendance - Điểm danh trong ngày
   * @param {Array} daySchedules - Lịch làm việc trong ngày
   * @returns {Object} Thống kê tổng với đầy đủ các trường
   */
  async calculateSimpleDaySummary(dayAttendance, daySchedules) {
    // Luôn sử dụng logic từ calculateStatistics để đảm bảo tính nhất quán
    const dayStats = await this.calculateStatistics(dayAttendance, daySchedules);

    // Tổng hợp từ user stats thành summary tổng
    const summary = this.aggregateUserStatsToSummary(dayStats);

    // Đảm bảo luôn trả về object đầy đủ các trường
    return summary;
  }

  /**
   * Tổng hợp user stats thành summary với đầy đủ các trường
   * @param {Array} userStats - Thống kê theo user
   * @returns {Object} Summary tổng với đầy đủ các trường
   */
  aggregateUserStatsToSummary(userStats) {
    const summary = {
      totalScheduled: 0,
      totalAttended: 0,
      onTime: 0,
      late: 0,
      absent: 0,
      nonattendance: 0,
      excused: 0,
      attendanceRate: 0
    };

    userStats.forEach(stat => {
      summary.totalScheduled += stat.totalScheduled;
      summary.totalAttended += stat.totalAttended;
      summary.onTime += stat.onTime;
      summary.late += stat.late;
      summary.absent += stat.absent;
      summary.nonattendance += stat.nonattendance || 0;
      summary.excused += stat.excused || 0;
    });

    summary.attendanceRate = summary.totalScheduled > 0
      ? Math.round((summary.totalAttended / summary.totalScheduled) * 100)
      : 0;

    return summary;
  }

  /**
   * Tính toán thống kê theo unit cho một ngày với đầy đủ các trường
   * @param {Array} dayAttendance - Điểm danh trong ngày
   * @param {Array} daySchedules - Lịch làm việc trong ngày
   * @param {String} unitFilter - ID unit để filter (optional)
   * @returns {Array} Thống kê theo unit với đầy đủ các trường
   */
  async calculateDayStatsByUnit(dayAttendance, daySchedules, unitFilter = null) {
    const Unit = require('../models/unit');

    // Sử dụng calculateStatistics để có logic đầy đủ
    const allUserStats = await this.calculateStatistics(dayAttendance, daySchedules);

    // Group user stats theo unit
    const statsByUnit = {};

    for (const userStat of allUserStats) {
      if (userStat.user && userStat.user.units && userStat.user.units.length > 0) {
        userStat.user.units.forEach(unit => {
          const unitId = unit._id || unit;
          if (!unitFilter || unitId.toString() === unitFilter) {
            if (!statsByUnit[unitId]) {
              statsByUnit[unitId] = {
                unitId,
                unitName: null,
                users: []
              };
            }
            statsByUnit[unitId].users.push(userStat);
          }
        });
      }
    }

    // Tính tổng thống kê cho từng unit
    const unitStats = [];
    for (const unitId of Object.keys(statsByUnit)) {
      const unitData = statsByUnit[unitId];

      // Lấy thông tin unit
      const unit = await Unit.findById(unitId, 'name').lean();

      // Tổng hợp thống kê từ các user trong unit
      const unitSummary = this.aggregateUserStatsToSummary(unitData.users);

      unitStats.push({
        unitId,
        unitName: unit ? unit.name : 'Không xác định',
        ...unitSummary
      });
    }

    return unitStats.sort((a, b) => a.unitName.localeCompare(b.unitName));
  }

  /**
   * Tính toán thống kê summary cho một ca cụ thể
   * @param {Array} dayAttendance - Điểm danh trong ngày
   * @param {Array} daySchedules - Lịch làm việc trong ngày (đã được filter theo shift)
   * @param {String} shift - Ca làm việc ('morning' hoặc 'afternoon')
   * @returns {Object} Thống kê summary cho ca được chỉ định
   */
  async calculateShiftSummary(dayAttendance, daySchedules, shift) {
    // Sử dụng calculateStatistics với dữ liệu đã được filter
    const shiftStats = await this.calculateStatistics(dayAttendance, daySchedules);

    // Tổng hợp thành summary
    const summary = this.aggregateUserStatsToSummary(shiftStats);

    return {
      ...summary,
      shift: shift,
      shiftName: shift === 'morning' ? 'Ca sáng' : 'Ca chiều'
    };
  }

  /**
   * Tính toán thống kê theo unit cho một ca cụ thể
   * @param {Array} dayAttendance - Điểm danh trong ngày
   * @param {Array} daySchedules - Lịch làm việc trong ngày (đã được filter theo shift)
   * @param {String} unitFilter - ID unit để filter (optional)
   * @param {String} shift - Ca làm việc
   * @returns {Array} Thống kê theo unit cho ca được chỉ định
   */
  async calculateDayStatsByUnitWithShift(dayAttendance, daySchedules, unitFilter = null, shift) {
    const Unit = require('../models/unit');

    // Sử dụng calculateStatistics với dữ liệu đã được filter theo shift
    const allUserStats = await this.calculateStatistics(dayAttendance, daySchedules);

    // Group user stats theo unit (logic tương tự calculateDayStatsByUnit)
    const statsByUnit = {};

    for (const userStat of allUserStats) {
      if (userStat.user && userStat.user.units && userStat.user.units.length > 0) {
        userStat.user.units.forEach(unit => {
          const unitId = unit._id || unit;
          if (!unitFilter || unitId.toString() === unitFilter) {
            if (!statsByUnit[unitId]) {
              statsByUnit[unitId] = {
                unitId,
                unitName: null,
                users: []
              };
            }
            statsByUnit[unitId].users.push(userStat);
          }
        });
      }
    }

    // Tính tổng cho từng unit và lấy tên unit
    const unitStats = [];
    for (const [unitId, unitData] of Object.entries(statsByUnit)) {
      const unit = await Unit.findById(unitId).lean();
      const unitSummary = this.aggregateUserStatsToSummary(unitData.users);

      unitStats.push({
        unitId,
        unitName: unit ? unit.name : 'Không xác định',
        shift: shift,
        shiftName: shift === 'morning' ? 'Ca sáng' : 'Ca chiều',
        ...unitSummary
      });
    }

    return unitStats.sort((a, b) => a.unitName.localeCompare(b.unitName));
  }

  /**
   * Tính toán breakdown thống kê theo từng ca (morning và afternoon)
   * @param {Array} dayAttendance - Điểm danh trong ngày
   * @param {Array} daySchedules - Lịch làm việc trong ngày
   * @returns {Object} Breakdown thống kê theo ca
   */
  async calculateShiftBreakdown(dayAttendance, daySchedules) {
    const breakdown = {};

    for (const shift of ['morning', 'afternoon']) {
      // Filter attendance records theo shift
      const shiftAttendance = dayAttendance.filter(record => record.shift === shift);

      // Filter work schedules theo shift
      const shiftSchedules = daySchedules.filter(schedule =>
        schedule.shifts.some(s => s.type === shift)
      ).map(schedule => ({
        ...schedule,
        shifts: schedule.shifts.filter(s => s.type === shift)
      }));

      // Tính thống kê cho shift này
      const shiftStats = await this.calculateStatistics(shiftAttendance, shiftSchedules);
      const summary = this.aggregateUserStatsToSummary(shiftStats);

      breakdown[shift] = {
        shift: shift,
        shiftName: shift === 'morning' ? 'Ca sáng' : 'Ca chiều',
        ...summary
      };
    }

    return breakdown;
  }

  /**
   * Tính toán thống kê từ dữ liệu (đã cải tiến để phân biệt absent và nonattendance)
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} workSchedules - Lịch làm việc
   * @returns {Object} Thống kê
   */
  async calculateStatistics(attendanceRecords, workSchedules) {
    const userStats = {};

    // Khởi tạo thống kê cho từng user
    workSchedules.forEach(schedule => {
      const userId = schedule.user._id.toString();
      if (!userStats[userId]) {
        userStats[userId] = {
          user: schedule.user,
          totalScheduled: 0,
          totalAttended: 0,
          onTime: 0,
          late: 0,
          absent: 0,
          nonattendance: 0,
          excused: 0, // Thêm trường mới cho đơn nghỉ được phê duyệt
          attendanceRate: 0,
          shiftString: _.includes(schedule.shifts.map(shift => shift.type), 'morning') && _.includes(schedule.shifts.map(shift => shift.type), 'afternoon')
          ? 'Cả ngày'
          : _.includes(schedule.shifts.map(shift => shift.type), 'morning')
            ? 'Ca sáng'
            : _.includes(schedule.shifts.map(shift => shift.type), 'afternoon')
              ? 'Ca chiều'
              : 'Cả ngày',
        };
      }
      userStats[userId].totalScheduled += schedule.shifts.length;
    });

    // Tính toán từ bản ghi điểm danh
    attendanceRecords.forEach(record => {
      const userId = record.user._id.toString();
      if (userStats[userId]) {
        userStats[userId].totalAttended++;

        if (record.status === 'on_time') {
          userStats[userId].onTime++;
        } else if (record.status === 'late') {
          userStats[userId].late++;
        }
      }
    });

    // Tính toán absent, nonattendance và excused cho từng user
    for (const userId of Object.keys(userStats)) {
      const stats = userStats[userId];
      const userSchedules = workSchedules.filter(s => s.user._id.toString() === userId);

      // Lấy danh sách đơn xin nghỉ được phê duyệt cho user này
      const approvedLeaves = await this.getApprovedLeaveRequests(userId, userSchedules);

      let absentCount = 0;
      let nonattendanceCount = 0;
      let excusedCount = 0;

      // Debug: Uncomment để kiểm tra dữ liệu khi cần debug
      // console.log(`\n=== DEBUG USER ${userId} ===`);
      // console.log(`Total scheduled: ${stats.totalScheduled}`);
      // console.log(`Total attended: ${stats.totalAttended}`);
      // console.log(`User schedules count: ${userSchedules.length}`);
      // console.log(`Approved leaves count: ${approvedLeaves.length}`);

      // Duyệt qua từng ca làm việc để phân loại
      for (const schedule of userSchedules) {
        for (const shift of schedule.shifts) {
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === userId &&
            record.date === schedule.date &&
            record.shift === shift.type
          );

          // console.log(`Shift ${schedule.date} ${shift.type}: hasAttendance=${hasAttendance}, status=${shift.status}`);

          if (!hasAttendance) {
            // Ưu tiên sử dụng trạng thái từ WorkSchedule nếu có
            if (shift.status === 'excused') {
              excusedCount++;
              // console.log(`  -> EXCUSED from WorkSchedule (count: ${excusedCount})`);
              continue;
            } else if (shift.status === 'absent') {
              absentCount++;
              // console.log(`  -> ABSENT from WorkSchedule (count: ${absentCount})`);
              continue;
            }

            // Fallback: Tính toán theo logic cũ nếu WorkSchedule chưa có trạng thái
            const hasApprovedLeave = this.hasApprovedLeaveForShift(
              approvedLeaves,
              schedule.date,
              shift.type
            );

            // console.log(`  -> hasApprovedLeave=${hasApprovedLeave}`);

            if (hasApprovedLeave) {
              // Có đơn xin nghỉ được phê duyệt -> tính vào excused
              excusedCount++;
              // console.log(`  -> EXCUSED (count: ${excusedCount})`);
              continue;
            }

            // Sử dụng logic tương tự getShiftStatus để xác định trạng thái
            const shiftStatus = this.getShiftStatus(shift, null, schedule.date);

            if (shiftStatus === 'absent') {
              absentCount++;
              // console.log(`  -> ABSENT (count: ${absentCount})`);
            } else if (shiftStatus === 'nonattendance') {
              nonattendanceCount++;
              // console.log(`  -> NONATTENDANCE (count: ${nonattendanceCount})`);
            } else {
              // Fallback cho các trạng thái khác (scheduled, v.v.)
              // Kiểm tra ca làm việc đã kết thúc chưa
              const isCompleted = this.isShiftCompleted(schedule.date, shift.type);

              if (isCompleted) {
                absentCount++; // Ca đã kết thúc mà không có chấm công -> absent
                // console.log(`  -> ABSENT fallback (count: ${absentCount})`);
              } else {
                nonattendanceCount++; // Ca chưa kết thúc -> nonattendance
                // console.log(`  -> NONATTENDANCE fallback (count: ${nonattendanceCount})`);
              }
            }
          }
        }
      }

      stats.absent = absentCount;
      stats.nonattendance = nonattendanceCount;
      stats.excused = excusedCount;
      stats.attendanceRate = stats.totalScheduled > 0
        ? Math.round((stats.totalAttended / stats.totalScheduled) * 100)
        : 0;

      // Add current shift status information
      try {
        const currentShiftStatus = await this.getCurrentShiftStatus(
          userId,
          userSchedules,
          attendanceRecords.filter(r => r.user._id.toString() === userId),
          approvedLeaves
        );
        stats.currentShiftStatus = currentShiftStatus;
      } catch (error) {
        console.error(`Error getting current shift status for user ${userId}:`, error);
        stats.currentShiftStatus = {
          hasSchedule: false,
          status: 'no_schedule',
          description: 'Không thể xác định trạng thái ca hiện tại',
          currentShift: null,
          shiftDisplayName: null,
          checkinTime: null
        };
      }

      // Debug: Uncomment để kiểm tra tổng khi cần debug
      // const total = stats.totalAttended + stats.absent + stats.nonattendance + stats.excused;
      // console.log(`Final counts: attended=${stats.totalAttended}, absent=${stats.absent}, nonattendance=${stats.nonattendance}, excused=${stats.excused}`);
      // console.log(`Total check: ${total} === ${stats.totalScheduled} ? ${total === stats.totalScheduled}`);
      // if (total !== stats.totalScheduled) {
      //   console.warn(`⚠️  MISMATCH: Total (${total}) !== totalScheduled (${stats.totalScheduled})`);
      // }
    }

    return Object.values(userStats);
  }

  /**
   * Lấy danh sách đơn xin nghỉ được phê duyệt cho user trong khoảng thời gian
   * @param {String} userId - ID user
   * @param {Array} schedules - Danh sách lịch làm việc
   * @returns {Array} Danh sách đơn xin nghỉ được phê duyệt
   */
  async getApprovedLeaveRequests(userId, schedules) {
    if (!schedules || schedules.length === 0) {
      return [];
    }

    // Lấy khoảng thời gian từ schedules
    const dates = schedules.map(s => s.date);
    const startDate = dates.sort()[0];
    const endDate = dates.sort().reverse()[0];

    try {
      const approvedLeaves = await LeaveRequest.find({
        user: userId,
        status: 'approved',
        $or: [
          // Đơn nghỉ phép có khoảng thời gian
          {
            type: 'leave',
            startDate: { $lte: endDate },
            endDate: { $gte: startDate }
          },
          // Đơn nghỉ đột xuất hoặc đi muộn trong ngày cụ thể
          {
            type: { $in: ['emergency_leave', 'late_arrival'] },
            startDate: { $gte: startDate, $lte: endDate }
          }
        ]
      }).lean();

      return approvedLeaves;
    } catch (error) {
      console.error('Error getting approved leave requests:', error);
      return [];
    }
  }

  /**
   * Kiểm tra có đơn xin nghỉ được phê duyệt cho ca làm việc cụ thể
   * @param {Array} approvedLeaves - Danh sách đơn xin nghỉ được phê duyệt
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Có đơn xin nghỉ được phê duyệt hay không
   */
  hasApprovedLeaveForShift(approvedLeaves, date, shiftType) {
    return approvedLeaves.some(leave => {
      // Đơn nghỉ phép (có thể nhiều ngày)
      if (leave.type === 'leave') {
        return DateUtils.compareDDMMYYYY(leave.startDate, date) <= 0 &&
          DateUtils.compareDDMMYYYY(leave.endDate, date) >= 0;
      }

      // Đơn nghỉ đột xuất hoặc đi muộn (trong ngày)
      if (leave.type === 'emergency_leave' || leave.type === 'late_arrival') {
        if (leave.startDate !== date) {
          return false;
        }

        // Kiểm tra ca làm việc
        if (leave.shift === 'both') {
          return true; // Nghỉ cả ngày
        }

        return leave.shift === shiftType;
      }

      return false;
    });
  }

  /**
   * Kiểm tra ca làm việc đã kết thúc chưa
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Ca làm việc đã kết thúc hay chưa
   */
  isShiftCompleted(date, shiftType) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();

    // Nếu là ngày trong tương lai, chưa kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) > 0) {
      return false;
    }

    // Nếu là ngày trong quá khứ, đã kết thúc
    if (DateUtils.compareDDMMYYYY(date, currentDate) < 0) {
      return true;
    }

    // Nếu là ngày hôm nay, kiểm tra thời gian
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Thời gian kết thúc ca làm việc
    let shiftEndTime;
    if (shiftType === 'morning') {
      shiftEndTime = 12 * 60; // 12:00
    } else if (shiftType === 'afternoon') {
      shiftEndTime = 18 * 60; // 18:00
    }

    return currentTimeInMinutes > shiftEndTime;
  }

  /**
   * Kiểm tra ca làm việc đang diễn ra
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Ca làm việc đang diễn ra hay chưa
   */
  isShiftInProgress(date, shiftType) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();

    // Chỉ kiểm tra cho ngày hôm nay
    if (DateUtils.compareDDMMYYYY(date, currentDate) !== 0) {
      return false;
    }

    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Sử dụng constants cho thời gian checkin
    const shiftStartTime = shiftType === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START;

    const shiftEndTime = shiftType === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_END
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_END;

    return currentTimeInMinutes >= shiftStartTime && currentTimeInMinutes <= shiftEndTime;
  }

  /**
   * Kiểm tra ca làm việc chưa bắt đầu
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @param {String} shiftType - Loại ca ('morning' | 'afternoon')
   * @returns {Boolean} Ca làm việc chưa bắt đầu hay chưa
   */
  isShiftNotStarted(date, shiftType) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();

    // Nếu là ngày trong tương lai, chưa bắt đầu
    if (DateUtils.compareDDMMYYYY(date, currentDate) > 0) {
      return true;
    }

    // Nếu là ngày trong quá khứ, đã bắt đầu
    if (DateUtils.compareDDMMYYYY(date, currentDate) < 0) {
      return false;
    }

    // Nếu là ngày hôm nay, kiểm tra thời gian
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Sử dụng constants cho thời gian bắt đầu checkin
    const shiftStartTime = shiftType === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START;

    return currentTimeInMinutes < shiftStartTime;
  }

  /**
   * Check if user has conflicting meeting with work shift
   * @param {String} userId - User ID
   * @param {String} date - Date (DD-MM-YYYY)
   * @param {String} shiftType - Shift type ('morning' | 'afternoon')
   * @returns {Boolean} Has conflicting meeting or not
   */
  async hasConflictingMeeting(userId, date, shiftType) {
    try {
      // Chuyển đổi ngày từ DD-MM-YYYY sang timestamp
      const [day, month, year] = date.split('-');
      const dateObj = new Date(year, month - 1, day);

      // Xác định thời gian bắt đầu và kết thúc ca làm việc
      let shiftStartHour, shiftEndHour;
      if (shiftType === 'morning') {
        shiftStartHour = 8;
        shiftEndHour = 12;
      } else if (shiftType === 'afternoon') {
        shiftStartHour = 14;
        shiftEndHour = 18;
      }

      const shiftStartTime = new Date(dateObj);
      shiftStartTime.setHours(shiftStartHour, 0, 0, 0);

      const shiftEndTime = new Date(dateObj);
      shiftEndTime.setHours(shiftEndHour, 0, 0, 0);

      // Tìm lịch họp trùng thời gian
      const conflictingMeetings = await MeetingSchedule.find({
        officers: userId,
        status: 1,
        $or: [
          // Cuộc họp bắt đầu trong ca làm việc
          {
            startTime: {
              $gte: shiftStartTime.getTime(),
              $lt: shiftEndTime.getTime()
            }
          },
          // Cuộc họp kết thúc trong ca làm việc
          {
            endTime: {
              $gt: shiftStartTime.getTime(),
              $lte: shiftEndTime.getTime()
            }
          },
          // Cuộc họp bao trùm cả ca làm việc
          {
            startTime: { $lte: shiftStartTime.getTime() },
            endTime: { $gte: shiftEndTime.getTime() }
          }
        ]
      }).lean();

      return conflictingMeetings.length > 0;
    } catch (error) {
      console.error('Error checking conflicting meetings:', error);
      return false;
    }
  }

  /**
   * Determine current shift status for user
   * @param {String} userId - User ID
   * @param {Array} userSchedules - User's work schedules
   * @param {Array} attendanceRecords - User's attendance records
   * @param {Array} approvedLeaves - Approved leave requests
   * @returns {Object} Current shift status information
   */
  async getCurrentShiftStatus(userId, userSchedules, attendanceRecords, approvedLeaves) {
    const now = new Date();
    const currentDate = DateUtils.getCurrentDateDDMMYYYY();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Find today's work schedule
    const todaySchedule = userSchedules.find(s => s.date === currentDate);

    if (!todaySchedule || !todaySchedule.shifts || todaySchedule.shifts.length === 0) {
      return {
        hasSchedule: false,
        status: 'no_schedule',
        description: 'Hôm nay không có lịch làm việc',
        currentShift: null,
        shiftDisplayName: null,
        checkinTime: null
      };
    }

    // Determine current shift based on time
    let currentShiftObj = null;
    let currentShiftType = null;

    // Check morning shift - sử dụng constants
    if (currentTimeInMinutes >= CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START &&
        currentTimeInMinutes <= CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_END) {
      const morningShift = todaySchedule.shifts.find(s => s.type === 'morning');
      if (morningShift) {
        currentShiftObj = morningShift;
        currentShiftType = 'morning';
      }
    }
    // Check afternoon shift - sử dụng constants
    else if (currentTimeInMinutes >= CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START &&
             currentTimeInMinutes <= CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_END) {
      const afternoonShift = todaySchedule.shifts.find(s => s.type === 'afternoon');
      if (afternoonShift) {
        currentShiftObj = afternoonShift;
        currentShiftType = 'afternoon';
      }
    }

    // If no shift is currently in progress, find the nearest shift
    if (!currentShiftObj) {
      // Find upcoming or recently ended shift
      for (const shift of todaySchedule.shifts) {
        if (shift.type === 'morning') {
          if (currentTimeInMinutes < CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START) {
            // Morning shift hasn't started yet
            currentShiftObj = shift;
            currentShiftType = 'morning';
            break;
          } else if (currentTimeInMinutes <= CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_END + 30) {
            // Just ended morning shift (30 phút sau khi kết thúc)
            currentShiftObj = shift;
            currentShiftType = 'morning';
            break;
          }
        } else if (shift.type === 'afternoon') {
          if (currentTimeInMinutes < CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START) {
            // Afternoon shift hasn't started yet
            currentShiftObj = shift;
            currentShiftType = 'afternoon';
            break;
          } else if (currentTimeInMinutes <= CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_END + 30) {
            // Just ended afternoon shift (30 phút sau khi kết thúc)
            currentShiftObj = shift;
            currentShiftType = 'afternoon';
            break;
          }
        }
      }
    }

    if (!currentShiftObj) {
      return {
        hasSchedule: true,
        status: 'no_schedule',
        description: 'Hiện tại không trong thời gian ca làm việc nào',
        currentShift: null,
        shiftDisplayName: null,
        checkinTime: null
      };
    }

    // Check shift status
    const shiftDisplayName = currentShiftType === 'morning' ? 'Ca sáng' : 'Ca chiều';

    // Check if there's already an attendance record
    const attendanceRecord = attendanceRecords.find(record =>
      record.date === currentDate && record.shift === currentShiftType
    );

    if (attendanceRecord) {
      // Already checked in
      const statusType = attendanceRecord.status === 'on_time' ? 'on_time' : 'late';
      const statusDescription = attendanceRecord.status === 'on_time' ? 'đúng giờ' : 'muộn';
      return {
        hasSchedule: true,
        status: statusType,
        description: `${shiftDisplayName} - Đã chấm công ${statusDescription}`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: attendanceRecord.checkinTime
      };
    }

    // Check status from WorkSchedule
    if (currentShiftObj.status === 'excused') {
      return {
        hasSchedule: true,
        status: 'excused',
        description: `${shiftDisplayName} - Có đơn xin nghỉ được phê duyệt`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    if (currentShiftObj.status === 'business_trip') {
      return {
        hasSchedule: true,
        status: 'business_trip',
        description: `${shiftDisplayName} - Đang đi công tác`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    // Check approved leave requests
    const hasApprovedLeave = this.hasApprovedLeaveForShift(approvedLeaves, currentDate, currentShiftType);
    if (hasApprovedLeave) {
      return {
        hasSchedule: true,
        status: 'excused',
        description: `${shiftDisplayName} - Có đơn xin nghỉ được phê duyệt`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    // Check conflicting meetings
    // const hasConflictingMeeting = await this.hasConflictingMeeting(userId, currentDate, currentShiftType);
    // if (hasConflictingMeeting) {
    //   return {
    //     hasSchedule: true,
    //     status: 'conflicting_meeting',
    //     description: `${shiftDisplayName} - Có lịch họp trùng thời gian`,
    //     currentShift: currentShiftType,
    //     shiftDisplayName: shiftDisplayName,
    //     checkinTime: null
    //   };
    // }

    // Determine status based on time
    if (this.isShiftNotStarted(currentDate, currentShiftType)) {
      return {
        hasSchedule: true,
        status: 'pending',
        description: `${shiftDisplayName} - Chưa đến thời gian làm việc`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    if (this.isShiftInProgress(currentDate, currentShiftType)) {
      return {
        hasSchedule: true,
        status: 'nonattendance',
        description: `${shiftDisplayName} - Đang trong thời gian làm việc nhưng chưa chấm công`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    if (this.isShiftCompleted(currentDate, currentShiftType)) {
      return {
        hasSchedule: true,
        status: 'absent',
        description: `${shiftDisplayName} - Đã kết thúc ca làm việc mà không chấm công`,
        currentShift: currentShiftType,
        shiftDisplayName: shiftDisplayName,
        checkinTime: null
      };
    }

    return {
      hasSchedule: true,
      status: 'no_schedule',
      description: `${shiftDisplayName} - Không thể xác định trạng thái`,
      currentShift: currentShiftType,
      shiftDisplayName: shiftDisplayName,
      checkinTime: null
    };
  }
}

module.exports = new AttendanceService();