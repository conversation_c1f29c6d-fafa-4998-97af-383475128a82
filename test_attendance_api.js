/**
 * Test script cho API thống kê điểm danh chi tiết
 * Chạy: node test_attendance_api.js
 */

const axios = require('axios');

// Cấu hình API
const API_BASE_URL = 'http://localhost:9654';
let TEST_TOKEN = null; // Sẽ được lấy từ login

// Thông tin login test (cần thay bằng thông tin thực tế)
const LOGIN_CREDENTIALS = {
  username: 'admin', // Thay bằng username thực tế
  password: 'admin123' // Thay bằng password thực tế
};

// Hoặc sử dụng token giả để test (bỏ qua authentication)
const USE_FAKE_TOKEN = true;
const FAKE_TOKEN = 'fake-token-for-testing';

// Test data
const testData = {
  period: 'week',
  startDate: '01-08-2025',
  endDate: '07-08-2025'
};

// Function để login và lấy token
async function loginAndGetToken() {
  try {
    console.log('🔐 Đang login để lấy token...');

    const response = await axios.post(`${API_BASE_URL}/user/login`, LOGIN_CREDENTIALS, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data && response.data.code === 200 && response.data.token) {
      TEST_TOKEN = response.data.token;
      console.log('✅ Login thành công, token đã được lấy');
      return true;
    } else {
      console.error('❌ Login thất bại:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ Lỗi khi login:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
    return false;
  }
}

async function testAttendanceStatisticsAPI() {
  try {
    console.log('🚀 Bắt đầu test API thống kê điểm danh chi tiết...\n');

    // Test API call
    const response = await axios.post(`${API_BASE_URL}/statisticsDetail/attendance`, testData, {
      headers: {
        'Content-Type': 'application/json',
        'token': TEST_TOKEN
      }
    });

    console.log('✅ API Response Status:', response.status);
    console.log('📊 Response Data:');
    console.log(JSON.stringify(response.data, null, 2));

    // Kiểm tra cấu trúc response
    if (response.data && response.data.data) {
      const { summary, byDay, byUser } = response.data.data;

      console.log('\n📈 Kiểm tra cấu trúc dữ liệu:');
      console.log('- Summary:', summary ? '✅' : '❌');
      console.log('- ByDay:', Array.isArray(byDay) ? `✅ (${byDay.length} ngày)` : '❌');
      console.log('- ByUser:', Array.isArray(byUser) ? `✅ (${byUser.length} users)` : '❌');

      if (summary) {
        console.log('\n📋 Summary fields:');
        const requiredFields = [
          'totalEmployees', 'onTimeAttendance', 'lateAttendance',
          'nonAttendance', 'absent', 'excused', 'businessTrip', 'totalShifts'
        ];

        requiredFields.forEach(field => {
          console.log(`- ${field}:`, typeof summary[field] === 'number' ? `✅ (${summary[field]})` : '❌');
        });
      }
    }

  } catch (error) {
    console.error('❌ Lỗi khi test API:');

    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('Không nhận được response từ server');
      console.error('Request:', error.request);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test với các tham số khác nhau
async function testDifferentParameters() {
  const testCases = [
    {
      name: 'Test tuần',
      data: { period: 'week', startDate: '01-08-2025', endDate: '07-08-2025' }
    },
    {
      name: 'Test tháng',
      data: { period: 'month', startDate: '01-08-2025', endDate: '31-08-2025' }
    },
    {
      name: 'Test quý',
      data: { period: 'quarter', startDate: '01-07-2025', endDate: '30-09-2025' }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🧪 ${testCase.name}:`);
    try {
      const response = await axios.post(`${API_BASE_URL}/statisticsDetail/attendance`, testCase.data, {
        headers: {
          'Content-Type': 'application/json',
          'token': TEST_TOKEN
        }
      });

      console.log(`✅ ${testCase.name} - Status: ${response.status}`);
      if (response.data && response.data.data) {
        const { summary } = response.data.data;
        console.log(`📊 Total Employees: ${summary?.totalEmployees || 0}`);
        console.log(`📊 Total Shifts: ${summary?.totalShifts || 0}`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name} - Error:`, error.response?.status || error.message);
    }
  }
}

// Test validation
async function testValidation() {
  console.log('\n🔍 Test validation:');

  const invalidCases = [
    {
      name: 'Missing period',
      data: { startDate: '01-08-2025', endDate: '07-08-2025' }
    },
    {
      name: 'Invalid period',
      data: { period: 'invalid', startDate: '01-08-2025', endDate: '07-08-2025' }
    },
    {
      name: 'Invalid date format',
      data: { period: 'week', startDate: '2025-08-01', endDate: '07-08-2025' }
    },
    {
      name: 'Start date > End date',
      data: { period: 'week', startDate: '07-08-2025', endDate: '01-08-2025' }
    }
  ];

  for (const testCase of invalidCases) {
    try {
      const response = await axios.post(`${API_BASE_URL}/statisticsDetail/attendance`, testCase.data, {
        headers: {
          'Content-Type': 'application/json',
          'token': TEST_TOKEN
        }
      });
      console.log(`❌ ${testCase.name} - Should have failed but got status: ${response.status}`);
    } catch (error) {
      console.log(`✅ ${testCase.name} - Correctly failed with status: ${error.response?.status}`);
    }
  }
}

// Chạy tất cả tests
async function runAllTests() {
  if (USE_FAKE_TOKEN) {
    console.log('🔧 Sử dụng fake token để test (bỏ qua authentication)');
    TEST_TOKEN = FAKE_TOKEN;
  } else {
    // Đầu tiên login để lấy token
    const loginSuccess = await loginAndGetToken();
    if (!loginSuccess) {
      console.error('❌ Không thể login, dừng test');
      return;
    }
  }

  await testAttendanceStatisticsAPI();
  await testDifferentParameters();
  await testValidation();

  console.log('\n🎉 Hoàn thành tất cả tests!');
}

// Chạy tests
runAllTests().catch(console.error);
